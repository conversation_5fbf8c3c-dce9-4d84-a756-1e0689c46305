// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { random } = require('faker');
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage');


module.exports = {

    //新建优惠码
    async CreateNewCoupons() {
        I.amOnPage('/system/goldengo/powerCenter/discountCode')
        global.couponCode = "UI自动化优惠码" + random.alpha(4) + Date.now()
        await basepage.moveCursorAndClick(I, '新建')
        await basepage.moveCursorAndClick(I, '优惠码批次名称', couponCode)

        if (randomInt(1, 3) == 2) { //优惠码使用类型：多次使用
            await basepage.moveCursorAndClick(I, '多次使用')
        }

        if (randomInt(1, 3) == 2) { //优惠模式：兑换
            await basepage.moveCursorAndClick(I, '兑换')
        }

        await basepage.editTime()
        await basepage.elementOperate('//*[@placeholder="请选择申请人"]').click()
        await basepage.elementOperate('//*[@placeholder="搜索"]').fillField('中台自动化')
        I.wait(2)
        await basepage.elementOperate("//*[@class='simulate-checkbox']").click()
        await basepage.elementOperate("//*[@title='返回']").click()
        I.wait(4)
        await basepage.moveCursorAndClick(I, '确 定')
        I.waitForText('中台自动化', 20)
        I.wait(2)
        await basepage.elementOperate("保存草稿").click()
        I.see("新增成功")
    },

    //查询
    async SearchNewCoupons() {
        I.amOnPage("/system/goldengo/powerCenter/discountCode")
        await basepage.moveCursorAndClick(I, '优惠券名称', "启用状态置灰逻辑测试")
        await basepage.moveCursorAndClick(I, '查 询')
        I.waitForText("启用状态置灰逻辑测试", 20)
        I.wait(2)
    },

    //编辑
    async EditNewCoupons() {
        await basepage.moveCursorAndClick(I, '编辑')
        I.switchToNextTab();
        I.wait(4)
        await basepage.elementOperate('保存草稿').click()
        I.see('编辑成功')
    },

    //产品列表
    async ProductListNewCoupons() {
        await this.SearchNewCoupons()
        await basepage.moveCursorAndClick(I, '产品列表')
        await basepage.goToNewTab(I)
        I.wait(4)
        I.waitForText("适用全部项目的CRM启用状态课程：", 20)
    },

    //状态启用
    async EnableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "启用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("优惠码不在有效使用时段", 20)
    },

    //状态禁用
    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "停用", null, null, null, null, 3)
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("优惠码参数错误", 20)
    },

}