// 创建人：李季
// 创建日期：2025-05-27
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const basepage = require("../../tools/basepage")
const apiHelper = require("../../tools/apiHelper")

const OrderParameter = {
    url:"https://next.gaodun.com/system/goldengo/powerCenter/order",
    interfaceUrl:"https://apigateway.gaodun.com/crmorder/api/v2/order/order-activity-page"
};

Feature('权益中心-权益订单');

Before(async ({ login }) => {
    await login('Testzhongtai');
});

Scenario('权益订单-查询订单', async ({ I }) => {
    await basepage.openPage(OrderParameter.url)
    let response;
    // 1. 先获取token
    const token = await apiHelper.getAccessToken({
        loginUrl: '/api/v4/vigo/login',
        appid: '210666',
        GDSID: 'fF1pz73ueD9USYAgwk2N4eQ7GTLJRDWYWpKhx33D',
        user: '<EMAIL>',
        password: 'xLIhavzYupaC2WcE1e8udV42vWQpk2yIAzP4xS+YVteinR1npWM=',
    });
    const zaihe = {
        "pageIndex": 1,
        "pageNum": 1,
        "pageSize": 10,
    }
    response = await apiHelper.post(
        OrderParameter.interfaceUrl,
        zaihe,
        {},
        false,
        token
    );
    const { data } = response;
    const { result } = data;
    orderNumber1 = result.list[0].orderNo
    orderNumber2 = result.list[1].orderNo
    await basepage.moveCursorAndClick(I, "订单编号", orderNumber1, 100)
    await basepage.moveCursorAndClick(I, "查 询")
    I.wait(4)
    I.dontSee(orderNumber2)

});
