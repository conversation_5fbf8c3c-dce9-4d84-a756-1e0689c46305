// 创建人：李季
// 创建日期：2025-01-03
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { random } = require('faker');
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage')

global.siteLink = 0

module.exports = {
    siteLink, //判断是否点击官网链接

    async SearchNewCoupons() {
        I.amOnPage("/system/goldengo/powerCenter/discount")
        await basepage.moveCursorAndClick(I, "优惠券名称", "scrm阿米巴优惠券测试生产-1")
        await basepage.moveCursorAndClick(I, "查 询")
        I.waitForText("scrm阿米巴优惠券测试生产-1")
        I.wait(2)
    },

    async EditNewCoupons() {
        await basepage.moveCursorAndClick(I, "编辑")
        await basepage.moveCursorAndClick(I, "优惠券展示副标题", 2, "编辑" + coupon)
        await basepage.moveCursorAndClick(I, "保存草稿")
    },

    async ProductListNewCoupons() {
        I.wait(2)
        await this.SearchNewCoupons()
        await basepage.moveCursorAndClick(I, "产品列表")
        I.waitForText("适用CRM项目范围：", 20)
    },

    async EnableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "启用")
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("领取有效期到期之后，启用状态为停用，不可变更。", 20)
    },

    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "修改状态")
        await basepage.moveCursorAndClick(I, "停用", null, null, null, null, 3)
        await basepage.moveCursorAndClick(I, "确 定")
        I.waitForText("修改状态与原始状态一致", 20)
    },

}
