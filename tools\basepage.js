const { I } = inject();
const axios = require('axios');
const fs = require('fs');
const util = require('util');
const setTimeoutPromise = util.promisify(setTimeout);
const qs = require('qs');


//元素操作的方法封装
const elementOperate = (element) => {
  return {
    //点击操作
    async click() {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 20)
        I.click(element);
      } else {
        I.waitForText(element, 20)
        I.click(element)
      }
    },
    //强制点击
    async forceClick() {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 20)
        I.forceClick(element);
      } else {
        I.waitForText(element, 20)
        I.forceClick(element)
      }
    },
    //输入内容
    async fillField(value) {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 20)
        I.fillField(element, value);
      } else {
        I.waitForText(element, 20)
        I.fillField(element, value)
      }
    },

    //添加内容
    async appendField(value) {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 20)
        I.appendField(element, value);
      } else {
        I.waitForText(element, 20)
        I.appendField(element, value)
      }
    },

    //获取某个元素标签属性的值
    async getGrabAttributeFrom(tag) {
      I.waitForVisible(element, 10)
      return await I.grabAttributeFrom(element, tag);
    },
    //获取元素的文本值
    async getGrabTextFrom() {
      I.waitForVisible(element, 10)
      return await I.grabTextFrom(element);
    },

    //清除文本
    async clear() {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 20)
        I.clearField(element);
      } else {
        I.waitForText(element, 20)
        I.clearField(element)
      }
    },

    // 图片上传
    async upload(file) {
      if (element.startsWith('/') || element.startsWith('.') || element.startsWith('#') || element.startsWith('(')) {
        I.waitForElement(element, 10)
        I.attachFile(element, file);
      } else {
        I.waitForText(element, 10)
        I.attachFile(element, file)
      }

    },
    //鼠标悬浮
    async hover() {
      I.waitForVisible(element, 10)
      I.hover(element)
    }

  };
};

//等待
function waitTime(time) {
  I.wait(time)
}

//访问指定url
function openUrl(url) {
  I.amOnPage(url)
}

//模拟键盘操作
function keyboardOperate(direction) {
  if (direction == 'ArrowDown') {
    I.pressKey(direction)
  } else if ('Enter') {
    I.pressKey(direction)
  } else if ('down') {
    I.pressKeyDown()
  } else if ("up") {
    I.pressKeyUp()
  } else if ("left") {
    I.pressKey('ArrowLeft')
  } else if ("right") {
    I.pressKey('ArrowRight')
  }
}


//时间组件通用
async function editTime(num = 1) {
  const startDateXPath = `(//*[@placeholder='开始日期']) [${num}]`;
  const endDateXPath = `(//*[@placeholder='结束日期']) [${num}]`;
  this.elementOperate(startDateXPath).click();
  this.elementOperate(startDateXPath).fillField("2023-04-01 00:00:00");
  this.elementOperate("//button[@class='ant-btn ant-btn-primary ant-btn-sm']").click();
  this.elementOperate(endDateXPath).click();
  this.elementOperate(endDateXPath).fillField("2030-04-01 23:59:59");
  this.elementOperate("//button[@class='ant-btn ant-btn-primary ant-btn-sm']").click();
}


// 创建一个函数，用于生成查找包含特定文本的span元素的XPath表达式
async function getXpathForSpanWithText(text, num = 1) {
  if (num && num > 1) {
    return `(//*[text()="${text}"])[${num}]`;
  }
  return `//*[text()="${text}"]`;
}

//点击和输入文本的方法
async function moveCursorAndClick(I, text, inputText = '', distance_X = 76, distance_Y = 0, container = null, num = 1) {
  let page;
  //检查是否已经传递了Playwright实例，如果没有，则从容器中获取
  if (container) {
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from provided container.");
  } else {
    const { container } = require('codeceptjs');
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from CodeceptJS container.");
  }

  // 生成XPath表达式
  let xpath = await getXpathForSpanWithText(text, num);
  console.log(`XPath for text "${text}": ${xpath}`);

  try {
    await setTimeoutPromise(1500);
    await I.waitForText(text, 20);
    await I.scrollTo(xpath);

    let location = await I.grabElementBoundingRect(xpath);
    console.log(`Element found at x=${location.x}, y=${location.y}`);

    // 计算元素的中心位置
    let targetX = location.x + location.width / 2;
    let targetY = location.y + location.height / 2;
    console.log(`Target click position set to x=${targetX}, y=${targetY}`);

    if (inputText) {
      await page.mouse.click(targetX + distance_X, targetY + distance_Y);
      await page.keyboard.type(inputText);
      console.log(`Input text "${inputText}" was typed.`);
    } else {
      if (distance_X != 76) {
        await page.mouse.click(targetX + distance_X, targetY);
        console.log(`xpath was typed.`);
      }
      await page.mouse.click(targetX, targetY);
      console.log(`xpath was typed.`);
    }
  } catch (error) {
    console.error("An error occurred:", error);
  } finally {
    await setTimeoutPromise(2000);
  }

};

async function getAcceessToken() {
  // 定义请求的 URL
  const url = 'https://t-apigateway.gaodun.com/api/v4/vigo/login';
  // 定义表单数据
  const formData = {
    GDSID: 'RVRiSkVDeEJEZHBFeHd5dlVWSW5tZFNGc2xXZFFsVkQ=',
    appid: '210301',
    password: 'gaodun123@',
    user: '<EMAIL>'
  };
  let accessToken
  // 发送 POST 请求
  await axios.post(url, qs.stringify(formData), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
    .then(response => {
      // 从响应数据中提取 accessToken
      accessToken = response.data.accessToken;
      console.log('提取的 accessToken:', accessToken);
    })
    .catch(error => {
      console.error('请求出错:', error);
    });
  return accessToken
}

async function clear(I, text, distance_X = 76, distance_Y = 0, container = null, num = 1) {
  let page;
  // 检查是否已经传递了Playwright实例，如果没有，则从容器中获取
  if (container) {
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from provided container.");
  } else {
    const { container } = require('codeceptjs');
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from CodeceptJS container.");
  }

  // 生成XPath表达式
  let xpath = await getXpathForSpanWithText(text, num);
  console.log(`XPath for text "${text}": ${xpath}`);

  try {
    await I.waitForText(text, 20);
    await I.scrollTo(xpath);
    let location = await I.grabElementBoundingRect(xpath);
    console.log(`Element found at x=${location.x}, y=${location.y}`);

    // 计算元素的中心位置
    let targetX = location.x + location.width / 2;
    let targetY = location.y + location.height / 2;
    console.log(`Target click position set to x=${targetX}, y=${targetY}`);

    //点击全选再清空
    await page.mouse.click(targetX + distance_X, targetY + distance_Y);
    const isMac = process.platform === 'darwin';
    await page.keyboard.down(isMac ? 'Meta' : 'Control');
    await page.keyboard.press('A');
    await page.keyboard.up(isMac ? 'Meta' : 'Control');
    await page.keyboard.press('Delete');

    await setTimeoutPromise(1000);
  } catch (error) {
    console.error("An error occurred:", error);
  }
};

async function openPage(url) {
  try {
    I.amOnPage(url);
    console.log(`Successfully opened the page: ${url}`);
  } catch (error) {
    console.error(`Failed to open the page: ${url}. Error:`, error);
    throw error;
  }
};

//打开新标签页
async function goToNewTab(I, timeout = 10) {
  // 1. 等待新标签页打开
  await I.wait(2);
  // 2. 尝试切换到新标签页
  try {
    await I.switchToNextTab();
    console.log('已切换到新标签页');
  } catch (switchError) {
    // 如果切换失败，尝试使用其他方法
    await I.executeScript(() => {
      window.open('', '_blank');
    });
    await I.wait(1);
    await I.switchToNextTab();
    console.log('使用备用方法切换到新标签页');
  }
  // 3. 等待新页面加载完成
  await I.waitForElement('body', timeout);
  console.log('新页面加载完成');
  // 4. 等待页面完全加载
  await I.wait(2);

}


//鼠标拖动
async function mouseDrag(I, text, distance_X = 76, distance_Y = 0, container = null, num = 1) {
  let page;
  //检查是否已经传递了Playwright实例，如果没有，则从容器中获取
  if (container) {
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from provided container.");
  } else {
    const { container } = require('codeceptjs');
    const helper = container.helpers('Playwright');
    page = helper.page;
    console.log("Playwright instance obtained from CodeceptJS container.");
  }

  // 生成XPath表达式
  let xpath = await getXpathForSpanWithText(text, num);
  console.log(`XPath for text "${text}": ${xpath}`);

  try {
    await I.waitForText(text, 20);
    await I.scrollTo(xpath);

    let location = await I.grabElementBoundingRect(xpath);
    console.log(`Element found at x=${location.x}, y=${location.y}`);

    // 计算元素的中心位置
    let targetX = location.x + location.width / 2;
    let targetY = location.y + location.height / 2;
    console.log(`Target click position set to x=${targetX}, y=${targetY}`);

    // 鼠标移动到元素中心
    await page.mouse.move(targetX, targetY);
    // 鼠标按下
    await page.mouse.down();
    // 鼠标往右拖动指定像素
    await page.mouse.move(targetX + distance_X, targetY);
    // 鼠标松开
    await page.mouse.up();

  } catch (error) {
    console.error("An error occurred:", error);
  } finally {
    await setTimeoutPromise(1000);
  }

};





module.exports = {
  elementOperate,
  waitTime, openUrl,
  keyboardOperate,
  editTime,
  getXpathForSpanWithText,
  moveCursorAndClick,
  clear,
  getAcceessToken,
  openPage,
  goToNewTab,
  mouseDrag,
};
