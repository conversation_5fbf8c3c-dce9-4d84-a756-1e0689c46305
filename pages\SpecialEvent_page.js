// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { random } = require('faker');
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage')
const util = require('util');
const setTimeoutPromise = util.promisify(setTimeout);

module.exports = {
    //查询
    async SearchEvent() {
        I.amOnPage('/system/goldengo/powerCenter/special')
        await basepage.moveCursorAndClick(I, "活动名称", "测试11")
        await basepage.moveCursorAndClick(I, "查 询")
        I.waitForText("测试11", 20)
        I.wait(2)
    },

    //编辑
    async EditEvent() {
        await basepage.moveCursorAndClick(I, "编辑")
        await basepage.moveCursorAndClick(I, "保存并下一步")
        I.waitForText("保存成功!", 20)
        I.wait(2)
        await basepage.moveCursorAndClick(I, "发 布")
        I.waitForText('保存成功!', 20)
    },


    //投放渠道
    async DeliveryChannel() {
        await basepage.moveCursorAndClick(I, "投放渠道")
        await basepage.moveCursorAndClick(I, "新增渠道")
        I.wait(2)
        await basepage.moveCursorAndClick(I, "请选择渠道", "测试")
        await basepage.moveCursorAndClick(I, "测试23", null, null, null, null, 3)
        await basepage.moveCursorAndClick(I, "请输入姓名", "中台")
        I.pressKey('Enter')
        I.pressKey('Tab')
        await basepage.moveCursorAndClick(I, '确 定')
        I.waitForText('新增渠道成功!', 20)
        await basepage.moveCursorAndClick(I, '复制链接')
        I.waitForText('复制成功！', 20)
    },


    //状态启用
    async EnableStatus() {
        await basepage.moveCursorAndClick(I, "停用")
        I.waitForText('启用', 20)
    },

    //状态停用
    async DisableStatus() {
        await basepage.moveCursorAndClick(I, "启用")
        I.waitForText('停用', 20)
    },


}