// 创建人：李季
// 创建日期：2025-01-09
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const { I, } = inject();
const { random } = require('faker');
const { randomInt } = require('crypto');
const basepage = require('../tools/basepage');
const axios = require('axios');
const syncLearnCard = require('../tools/syncOrder_learnCard')

global.cardPlatform = 0
global.activateSCRMiniCourse = 0
global.projectQRCode = 0

module.exports = {
    cardPlatform,
    activateSCRMiniCourse,
    projectQRCode,

    //搜索
    async SearchCard() {
        I.amOnPage('/system/goldengo/powerCenter/studyCard')
        await basepage.moveCursorAndClick(I, "卡组名称", "通用课程激活链路0422")
        await basepage.moveCursorAndClick(I, "查 询")
        I.waitForText("通用课程激活链路0422", 20)
        I.wait(2)
    },

    //生成学习卡
    async GenerateCard() {
        await basepage.moveCursorAndClick(I, "生成学习卡")
        await basepage.moveCursorAndClick(I, "生成数量", "100")
        await basepage.moveCursorAndClick(I, "领取/激活截止时间", '2030-12-31 23:59:59')
        await basepage.moveCursorAndClick(I, "确 定", null, null, null, null, 2)
        await basepage.moveCursorAndClick(I, "确 定")
    },

    //编辑
    async EditCard() {
        await basepage.moveCursorAndClick(I, "编辑卡组")
        await basepage.moveCursorAndClick(I, "保 存")
    },


}