// 创建人：李季
// 创建日期：2025-01-08
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const coupon = require("../../pages/CouponCode_page")
const basepage = require("../../tools/basepage")

Feature('权益中心-优惠码');

Before(async ({ login }) => {
    await login('Testzhongtai');
});

Scenario('优惠码-查询', async ({ I }) => {
    await coupon.SearchNewCoupons()
});

Scenario('优惠码-详情', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "详情")
    await basepage.goToNewTab(I)
    I.wait(4)
    I.waitForText("优惠码批次名称", 20)
});

Scenario('优惠码-产品列表', async ({ I }) => {
    await coupon.ProductListNewCoupons()
});

Scenario('优惠码-渠道配置', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "渠道配置")
    await basepage.goToNewTab(I)
    I.wait(4)
    I.waitForText("优惠码核销平台", 20)
});

Scenario('优惠码-使用统计', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await basepage.moveCursorAndClick(I, "使用统计")
    await basepage.goToNewTab(I)
    I.waitForText("生成总数", 20)
});

Scenario('优惠码-修改状态-启用-优惠码不在有效时段', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.EnableStatus()
});

Scenario('优惠码-修改状态-停用-优惠码参数错误', async ({ I }) => {
    await coupon.SearchNewCoupons()
    await coupon.DisableStatus()
});

