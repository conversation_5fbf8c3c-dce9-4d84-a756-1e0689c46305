// 创建人：李季
// 创建日期：2025-01-03
// 创建人工号：138693
// 创建人邮箱：<EMAIL>
const basepage = require("../../tools/basepage")
const voucher = require("../../pages/Voucher_page")

Feature('权益中心-优惠券');

Before(async ({ login }) => {
  await login('Testzhongtai');
});


Scenario('优惠券-查询', async ({ I }) => {
  await voucher.SearchNewCoupons()
});

Scenario('优惠券-详情', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "详情")
  I.waitForText("优惠券内部名称", 20)
});

Scenario('优惠券-产品列表', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.ProductListNewCoupons()
});

Scenario('优惠券-渠道配置', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "渠道配置")
  I.wait(2)
  await basepage.moveCursorAndClick(I, "保 存")
  I.waitForText("保存成功", 20)
});

Scenario('优惠券-官网链接', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "官网链接")
  await basepage.moveCursorAndClick(I, "复制链接")
  I.waitForText("复制成功！", 20)
  await basepage.moveCursorAndClick(I, "复制链接", null, null, null, null, 2)
  I.waitForText("复制成功！", 20)
});

Scenario('优惠券-使用统计', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await basepage.moveCursorAndClick(I, "使用统计")
  I.waitForText("发放总数", 20)
});

Scenario('优惠券-修改状态-启用-不可启用', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.EnableStatus()
});

Scenario('优惠券-修改状态-停用-状态一致', async ({ I }) => {
  await voucher.SearchNewCoupons()
  await voucher.DisableStatus()
});



